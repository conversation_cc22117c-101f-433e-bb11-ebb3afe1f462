import type { TelegramWebApp } from '~/types'

export const useTelegram = () => {
  const webApp = ref<TelegramWebApp | null>(null)
  const isAvailable = ref(false)
  const user = ref(null)
  const initData = ref('')

  // Initialize Telegram WebApp
  const init = () => {
    if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
      webApp.value = window.Telegram.WebApp
      isAvailable.value = true
      user.value = webApp.value.initDataUnsafe?.user || null
      initData.value = webApp.value.initData || ''

      // Configure WebApp
      webApp.value.ready()
      webApp.value.expand()
      
      // Set theme colors
      webApp.value.setHeaderColor('#1f2937') // gray-800
      webApp.value.setBackgroundColor('#111827') // gray-900

      console.log('[Telegram] WebApp initialized:', {
        platform: webApp.value.platform,
        version: webApp.value.version,
        user: user.value,
        hasInitData: !!initData.value
      })

      return webApp.value
    }
    
    console.warn('[Telegram] WebApp not available')
    return null
  }

  // Get viewport height
  const getViewportHeight = () => {
    if (webApp.value) {
      return webApp.value.viewportHeight
    }
    return typeof window !== 'undefined' ? window.innerHeight : 0
  }

  // Get stable viewport height
  const getStableViewportHeight = () => {
    if (webApp.value) {
      return webApp.value.viewportStableHeight
    }
    return getViewportHeight()
  }

  // Show main button
  const showMainButton = (text: string, onClick: () => void) => {
    if (webApp.value) {
      webApp.value.MainButton.setText(text)
      webApp.value.MainButton.onClick(onClick)
      webApp.value.MainButton.show()
    }
  }

  // Hide main button
  const hideMainButton = () => {
    if (webApp.value) {
      webApp.value.MainButton.hide()
    }
  }

  // Show back button
  const showBackButton = (onClick: () => void) => {
    if (webApp.value) {
      webApp.value.BackButton.onClick(onClick)
      webApp.value.BackButton.show()
    }
  }

  // Hide back button
  const hideBackButton = () => {
    if (webApp.value) {
      webApp.value.BackButton.hide()
    }
  }

  // Show popup
  const showPopup = async (params: {
    title?: string
    message: string
    buttons?: Array<{
      id: string
      type?: 'default' | 'ok' | 'close' | 'cancel' | 'destructive'
      text: string
    }>
  }) => {
    if (webApp.value) {
      return await webApp.value.showPopup(params)
    }
    return null
  }

  // Show alert
  const showAlert = (message: string) => {
    if (webApp.value) {
      webApp.value.showAlert(message)
    } else {
      alert(message)
    }
  }

  // Show confirm
  const showConfirm = async (message: string): Promise<boolean> => {
    if (webApp.value) {
      return await webApp.value.showConfirm(message)
    } else {
      return confirm(message)
    }
  }

  // Open link
  const openLink = (url: string) => {
    if (webApp.value) {
      webApp.value.openLink(url)
    } else {
      window.open(url, '_blank')
    }
  }

  // Send data to bot
  const sendData = (data: string) => {
    if (webApp.value) {
      webApp.value.sendData(data)
    }
  }

  // Close WebApp
  const close = () => {
    if (webApp.value) {
      webApp.value.close()
    }
  }

  // Haptic feedback
  const hapticFeedback = {
    impactOccurred: (style: 'light' | 'medium' | 'heavy' = 'medium') => {
      if (webApp.value && 'HapticFeedback' in webApp.value) {
        ;(webApp.value as any).HapticFeedback.impactOccurred(style)
      }
    },
    notificationOccurred: (type: 'error' | 'success' | 'warning') => {
      if (webApp.value && 'HapticFeedback' in webApp.value) {
        ;(webApp.value as any).HapticFeedback.notificationOccurred(type)
      }
    },
    selectionChanged: () => {
      if (webApp.value && 'HapticFeedback' in webApp.value) {
        ;(webApp.value as any).HapticFeedback.selectionChanged()
      }
    }
  }

  // Theme utilities
  const getThemeParams = () => {
    return webApp.value?.themeParams || {}
  }

  const isDarkTheme = () => {
    return webApp.value?.colorScheme === 'dark'
  }

  // Safe area utilities
  const getSafeAreaInset = () => {
    if (webApp.value && 'safeAreaInset' in webApp.value) {
      return (webApp.value as any).safeAreaInset
    }
    return { top: 0, bottom: 0, left: 0, right: 0 }
  }

  // Disable vertical swipes
  const disableVerticalSwipes = () => {
    if (webApp.value && 'disableVerticalSwipes' in webApp.value) {
      ;(webApp.value as any).disableVerticalSwipes()
    }
  }

  // Enable vertical swipes
  const enableVerticalSwipes = () => {
    if (webApp.value && 'enableVerticalSwipes' in webApp.value) {
      ;(webApp.value as any).enableVerticalSwipes()
    }
  }

  // Check if app is visible
  const isVisible = ref(true)
  
  const setupVisibilityHandlers = () => {
    if (webApp.value) {
      webApp.value.onEvent('viewportChanged', () => {
        console.log('[Telegram] Viewport changed')
      })
      
      webApp.value.onEvent('themeChanged', () => {
        console.log('[Telegram] Theme changed')
      })
    }
  }

  return {
    webApp: readonly(webApp),
    isAvailable: readonly(isAvailable),
    user: readonly(user),
    initData: readonly(initData),
    isVisible: readonly(isVisible),
    init,
    getViewportHeight,
    getStableViewportHeight,
    showMainButton,
    hideMainButton,
    showBackButton,
    hideBackButton,
    showPopup,
    showAlert,
    showConfirm,
    openLink,
    sendData,
    close,
    hapticFeedback,
    getThemeParams,
    isDarkTheme,
    getSafeAreaInset,
    disableVerticalSwipes,
    enableVerticalSwipes,
    setupVisibilityHandlers
  }
}
