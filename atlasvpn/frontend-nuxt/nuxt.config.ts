export default defineNuxtConfig({
  compatibilityDate: '2025-07-29',
  devtools: { enabled: false },


  
  // Server configuration to match current setup
  devServer: {
    port: 3005,
    host: '0.0.0.0'
  },

  // Modules for functionality
  modules: [
    '@nuxt/ui',
    '@pinia/nuxt'
  ],

  // Pinia configuration
  pinia: {
    storesDirs: ['./stores/**']
  },

  // App configuration
  app: {
    head: {
      title: 'AtlasVPN - Secure VPN Service',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1, user-scalable=no' },
        { name: 'description', content: 'Secure VPN service for your privacy and freedom' },
        { name: 'theme-color', content: '#030303' },
        { name: 'apple-mobile-web-app-capable', content: 'yes' },
        { name: 'mobile-web-app-capable', content: 'yes' },
        { name: 'apple-mobile-web-app-status-bar-style', content: 'black-translucent' },
        { name: 'apple-mobile-web-app-title', content: 'AtlasVPN' },
        { name: 'format-detection', content: 'telephone=no' },
        { name: 'msapplication-TileColor', content: '#000000' },
        { name: 'msapplication-config', content: '/browserconfig.xml' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'apple-touch-icon', href: '/icon-192x192.png' },
        { rel: 'manifest', href: '/manifest.json' }
      ]
    }
  },

  // Runtime config for API integration
  runtimeConfig: {
    public: {
      apiUrl: process.env.NUXT_PUBLIC_API_URL || 'https://app.atlasvip.cloud/api',
      wsBaseUrl: process.env.NUXT_PUBLIC_WS_BASE_URL || 'wss://app.atlasvip.cloud/ws',
      telegramWebappUrl: process.env.NUXT_PUBLIC_TELEGRAM_WEBAPP_URL || 'https://app.atlasvip.cloud',
      telegramBotUsername: process.env.NUXT_PUBLIC_TELEGRAM_BOT_USERNAME || '',
      environment: process.env.NODE_ENV || 'development'
    }
  },

  // CSS framework and global styles
  css: [
    '~/assets/css/main.css',
    '~/assets/css/animations.css',
    '~/assets/css/components.css'
  ],

  // Build optimization
  build: {
    transpile: ['@telegram-apps/sdk', 'three', '@tresjs/core']
  },

  // Vite configuration for compatibility
  vite: {
    define: {
      global: 'globalThis',
    },
    optimizeDeps: {
      include: ['@telegram-apps/sdk', 'three', 'axios', 'crypto-js']
    },
    server: {
      hmr: {
        port: 24678
      }
    },
    build: {
      // Chunk splitting for better caching
      rollupOptions: {
        output: {
          manualChunks: {
            'vendor': ['vue', 'vue-router'],
            'ui': ['@heroicons/vue'],
            'utils': ['date-fns', 'qrcode'],
            'three': ['three', '@tresjs/core']
          }
        }
      },
      // Optimize bundle size
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: process.env.NODE_ENV === 'production',
          drop_debugger: process.env.NODE_ENV === 'production'
        }
      }
    }
  },

  // SSR configuration for Telegram compatibility
  ssr: false, // Disable SSR for Telegram Mini App

  // TypeScript configuration
  typescript: {
    strict: true,
    typeCheck: false // Disable for now to avoid build issues
  },

  // Nitro configuration for production
  nitro: {
    preset: 'node-server',
    port: 3005,
    host: '0.0.0.0'
  },

  // Auto-imports configuration
  imports: {
    dirs: ['stores', 'composables', 'utils', 'types']
  },

  // Components auto-import
  components: [
    {
      path: '~/components',
      pathPrefix: false,
    },
  ],

  // Experimental features
  experimental: {
    payloadExtraction: false,
    viewTransition: true
  },

  // Bundle analyzer (enable with ANALYZE=true)
  ...(process.env.ANALYZE && {
    vite: {
      build: {
        rollupOptions: {
          plugins: [
            // Add bundle analyzer plugin when ANALYZE=true
          ]
        }
      }
    }
  })
})
