/* @nuxt/ui includes Tailwind CSS v4 automatically */
@import "@nuxt/ui";
@import "./animations.css";
@import "./components.css";

/* Ultra-Deep Black Theme with Purple Accents - Enhanced Design System */
@theme {
  /* Custom Colors */
  --color-atlas-black: #000000;
  --color-atlas-deep: #0a0a0a;
  --color-atlas-darker: #111111;
  --color-atlas-dark: #1a1a1a;
  --color-atlas-purple-50: #f3f0ff;
  --color-atlas-purple-100: #e9e2ff;
  --color-atlas-purple-200: #d6c9ff;
  --color-atlas-purple-300: #b8a3ff;
  --color-atlas-purple-400: #9575ff;
  --color-atlas-purple-500: #7c3aed;
  --color-atlas-purple-600: #6d28d9;
  --color-atlas-purple-700: #5b21b6;
  --color-atlas-purple-800: #4c1d95;
  --color-atlas-purple-900: #3c1361;
  --color-atlas-purple-950: #2e0a4f;

  /* Game-like Accent Colors */
  --color-atlas-gold: #ffd700;
  --color-atlas-gold-dark: #b8860b;
  --color-atlas-emerald: #10b981;
  --color-atlas-emerald-dark: #047857;
  --color-atlas-cyan: #06b6d4;
  --color-atlas-cyan-dark: #0891b2;
  --color-atlas-orange: #f97316;
  --color-atlas-orange-dark: #ea580c;

  /* Responsive Design System - Spacing Scale */
  --spacing-0: 0;
  --spacing-1: 0.25rem;    /* 4px */
  --spacing-2: 0.5rem;     /* 8px */
  --spacing-3: 0.75rem;    /* 12px */
  --spacing-4: 1rem;       /* 16px */
  --spacing-5: 1.25rem;    /* 20px */
  --spacing-6: 1.5rem;     /* 24px */
  --spacing-8: 2rem;       /* 32px */
  --spacing-10: 2.5rem;    /* 40px */
  --spacing-12: 3rem;      /* 48px */
  --spacing-16: 4rem;      /* 64px */
  --spacing-20: 5rem;      /* 80px */
  --spacing-24: 6rem;      /* 96px */
  --spacing-32: 8rem;      /* 128px */

  /* Typography Scale */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */
  --font-size-5xl: 3rem;       /* 48px */
  --font-size-6xl: 3.75rem;    /* 60px */
  --font-size-7xl: 4.5rem;     /* 72px */

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Responsive Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* Container Sizes */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}

/* Root Theme Variables */
:root {
  /* Primary Colors - Ultra Deep Black Theme */
  --ui-primary: var(--color-atlas-purple-500);
  --ui-secondary: var(--color-atlas-purple-600);
  --ui-success: var(--color-atlas-emerald);
  --ui-info: var(--color-atlas-cyan);
  --ui-warning: var(--color-atlas-orange);
  --ui-error: #ef4444;

  /* Background Colors - Ultra Deep Black */
  --ui-bg: var(--color-atlas-black);
  --ui-bg-muted: var(--color-atlas-deep);
  --ui-bg-elevated: var(--color-atlas-darker);
  --ui-bg-accented: var(--color-atlas-dark);
  --ui-bg-inverted: var(--color-white);

  /* Text Colors - High Contrast White */
  --ui-text-dimmed: #6b7280;
  --ui-text-muted: #9ca3af;
  --ui-text-toned: #d1d5db;
  --ui-text: #f9fafb;
  --ui-text-highlighted: var(--color-white);
  --ui-text-inverted: var(--color-atlas-black);

  /* Border Colors */
  --ui-border: var(--color-atlas-darker);
  --ui-border-muted: var(--color-atlas-deep);
  --ui-border-accented: var(--color-atlas-dark);
  --ui-border-inverted: var(--color-white);

  /* Game UI Colors */
  --ui-earn-primary: var(--color-atlas-gold);
  --ui-earn-secondary: var(--color-atlas-gold-dark);
  --ui-game-accent: var(--color-atlas-purple-400);
  --ui-game-highlight: var(--color-atlas-cyan);
}

/* Dark Mode Overrides (Always Active) */
.dark {
  --ui-primary: var(--color-atlas-purple-400);
  --ui-secondary: var(--color-atlas-purple-500);
  --ui-success: var(--color-atlas-emerald);
  --ui-info: var(--color-atlas-cyan);
  --ui-warning: var(--color-atlas-orange);
  --ui-error: #f87171;
}

/* Base Styles */
html {
  background-color: var(--ui-bg);
  color: var(--ui-text);
}

body {
  background-color: var(--ui-bg);
  color: var(--ui-text);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-overflow-scrolling: touch;
  -webkit-tap-highlight-color: transparent;
}

/* Custom Utility Classes */
.bg-atlas-black { background-color: var(--color-atlas-black); }
.bg-atlas-deep { background-color: var(--color-atlas-deep); }
.bg-atlas-darker { background-color: var(--color-atlas-darker); }
.bg-atlas-dark { background-color: var(--color-atlas-dark); }

.text-atlas-purple { color: var(--color-atlas-purple-500); }
.text-atlas-gold { color: var(--color-atlas-gold); }
.text-atlas-emerald { color: var(--color-atlas-emerald); }
.text-atlas-cyan { color: var(--color-atlas-cyan); }

.border-atlas-purple { border-color: var(--color-atlas-purple-500); }
.border-atlas-dark { border-color: var(--color-atlas-dark); }

/* Game-like Glow Effects */
.glow-purple {
  box-shadow: 0 0 20px rgba(124, 58, 237, 0.3);
}

.glow-gold {
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.glow-emerald {
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

/* Earn Tab Specific Styles */
.earn-card {
  background: linear-gradient(135deg, var(--color-atlas-deep) 0%, var(--color-atlas-darker) 100%);
  border: 1px solid var(--color-atlas-purple-800);
  transition: all 0.3s ease;
}

.earn-card:hover {
  border-color: var(--color-atlas-purple-500);
  box-shadow: 0 0 30px rgba(124, 58, 237, 0.2);
  transform: translateY(-2px);
}

.earn-button {
  background: linear-gradient(135deg, var(--color-atlas-gold) 0%, var(--color-atlas-gold-dark) 100%);
  color: var(--color-atlas-black);
  font-weight: 600;
  transition: all 0.3s ease;
}

.earn-button:hover {
  box-shadow: 0 0 25px rgba(255, 215, 0, 0.4);
  transform: scale(1.05);
}

/* Interactive Elements */
.interactive-element {
  transition: all 0.2s ease;
  cursor: pointer;
}

.interactive-element:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.15);
}

.interactive-element:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(124, 58, 237, 0.1);
}

/* Telegram WebApp specific styles */
.tg-viewport {
  height: 100vh;
  overflow: hidden;
}

.telegram-safe-area {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-atlas-deep);
}

::-webkit-scrollbar-thumb {
  background: var(--color-atlas-purple-600);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-atlas-purple-500);
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    var(--color-atlas-deep) 25%,
    var(--color-atlas-darker) 50%,
    var(--color-atlas-deep) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Focus Styles for Accessibility */
*:focus-visible {
  outline: 2px solid var(--color-atlas-purple-400);
  outline-offset: 2px;
}

/* Modern Responsive Design System */

/* Container System */
.container-responsive {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
}

@media (min-width: 640px) {
  .container-responsive {
    max-width: var(--container-sm);
    padding-left: var(--spacing-6);
    padding-right: var(--spacing-6);
  }
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: var(--container-md);
    padding-left: var(--spacing-8);
    padding-right: var(--spacing-8);
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    max-width: var(--container-lg);
  }
}

@media (min-width: 1280px) {
  .container-responsive {
    max-width: var(--container-xl);
  }
}

@media (min-width: 1536px) {
  .container-responsive {
    max-width: var(--container-2xl);
  }
}

/* Responsive Typography */
.text-responsive-xs { font-size: var(--font-size-xs); }
.text-responsive-sm { font-size: var(--font-size-sm); }
.text-responsive-base { font-size: var(--font-size-base); }
.text-responsive-lg { font-size: var(--font-size-lg); }
.text-responsive-xl { font-size: var(--font-size-xl); }

@media (min-width: 768px) {
  .text-responsive-xs { font-size: var(--font-size-sm); }
  .text-responsive-sm { font-size: var(--font-size-base); }
  .text-responsive-base { font-size: var(--font-size-lg); }
  .text-responsive-lg { font-size: var(--font-size-xl); }
  .text-responsive-xl { font-size: var(--font-size-2xl); }
}

/* Responsive Spacing Utilities */
.space-y-responsive > * + * {
  margin-top: var(--spacing-4);
}

@media (min-width: 768px) {
  .space-y-responsive > * + * {
    margin-top: var(--spacing-6);
  }
}

@media (min-width: 1024px) {
  .space-y-responsive > * + * {
    margin-top: var(--spacing-8);
  }
}

/* Mobile-First Responsive Breakpoints */
@media (max-width: 639px) {
  /* Mobile Styles */
  body {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
  }

  .hero-title {
    font-size: var(--font-size-4xl);
    line-height: var(--line-height-tight);
  }

  .hero-subtitle {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-normal);
  }

  .card-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .glow-purple,
  .glow-gold,
  .glow-emerald {
    box-shadow: none; /* Reduce glow effects on mobile for battery */
  }
}

@media (min-width: 640px) and (max-width: 767px) {
  /* Tablet Portrait Styles */
  .hero-title {
    font-size: var(--font-size-5xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-xl);
  }

  .card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-6);
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  /* Tablet Landscape Styles */
  .hero-title {
    font-size: var(--font-size-6xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-2xl);
  }

  .card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-8);
  }
}

@media (min-width: 1024px) {
  /* Desktop Styles */
  .hero-title {
    font-size: var(--font-size-7xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-3xl);
  }

  .card-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-10);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .earn-card:hover,
  .earn-button:hover,
  .interactive-element:hover {
    transform: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --ui-text: var(--color-white);
    --ui-border: var(--color-atlas-purple-400);
  }
}